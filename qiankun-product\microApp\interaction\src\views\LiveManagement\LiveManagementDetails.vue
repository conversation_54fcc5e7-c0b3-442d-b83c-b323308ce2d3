<template>
  <div class="LiveManagementDetails">
    <anchor-location>
      <div class="VideoMeetinDetailsTitle">{{ details?.theme }}</div>
      <div class="VideoMeetinDetailsTime">
        <div class="VideoMeetinDetailsTimeLeft">
          <div class="VideoMeetinDetailsTimeHours">{{ format(details.startTime, 'HH:mm') }}</div>
          <div class="VideoMeetinDetailsTimeDate">{{ format(details.startTime, 'YYYY年MM月DD日') }}</div>
        </div>
        <div class="VideoMeetinDetailsTimeCenter">
          <div class="VideoMeetinDetailsDuration">{{ details.during }}分钟</div>
          <!-- <div class="VideoMeetinDetailsStatus">{{ details.meetingStatus }}</div> -->
        </div>
        <div class="VideoMeetinDetailsTimeRight">
          <div class="VideoMeetinDetailsTimeHours">{{ format(details.endTime, 'HH:mm') }}</div>
          <div class="VideoMeetinDetailsTimeDate">{{ format(details.endTime, 'YYYY年MM月DD日') }}</div>
        </div>
      </div>
      <div class="LiveList">
        <div class="LiveName">直播简介</div>
        <div class="LiveContent">{{ details.liveDescribes }}</div>
      </div>
      <div class="LiveList" v-if="details.isReplay == 1 && details.liveReplayUrl">
        <div class="LiveName">直播回放</div>
        <div class="player-container">
          <video ref="videoPlayer" id="video-player" :src="details.liveReplayUrl" controls></video>
        </div>
      </div>
      <div class="LiveList">
        <div class="LiveInteractionBox">
          <div class="LiveName" style="margin-top: 0;">直播互动</div>
          <div class="LiveInteraction">
            <div class="LiveInteractionTotal">(共{{ totals }}条互动消息)</div>
            <el-button type="primary" @click="excelMsg()">导出互动消息</el-button>
          </div>
        </div>
        <el-scrollbar class="xyl-global-comment-scrollbar">

        </el-scrollbar>
        <div class="globalPagination">
          <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
            :total="totals" background />
        </div>
      </div>
    </anchor-location>
  </div>
</template>
<script>
export default { name: 'LiveManagementDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'common/js/time.js'

const route = useRoute()
const details = ref({})
const pageNo = ref(1)
const pageSize = ref(10)
const totals = ref(0)
onMounted(() => {
  if (route.query.id) { videoConnectionInfo() }
})
const videoConnectionInfo = async () => {
  const res = await api.videoConnectionInfo({ detailId: route.query.id })
  var { data } = res
  details.value = data
  handleQuery()
}
const excelMsg = () => {
  console.log('导出互动消息')
}
const handleQuery = () => {
  twoLevelTree()
}
const twoLevelTree = async () => {
  const { data, total } = await api.twoLevelTree({
    businessCode: 'liveBroadcast',
    businessId: route.query.id,
    pageNo: pageNo.value,
    pageSize: pageSize.value
  })
  console.log('data===>', data)
  console.log('total===>', total)
  // commentList.value = num || !props.scroll ? data : [...commentList.value, ...data]
  totals.value = total
  // loading.value = pageNo.value * pageSize.value < totals.value
  // isShow.value = pageNo.value * pageSize.value >= totals.value
}
</script>
<style lang="scss">
.LiveManagementDetails {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .anchor-location-body {
    padding: var(--zy-distance-one);
  }

  .VideoMeetinDetailsTitle {
    text-align: center;
    font-weight: bold;
    font-size: var(--zy-title-font-size);
    padding: var(--zy-distance-two) var(--zy-distance-one);
    line-height: var(--zy-line-height);
  }

  .VideoMeetinDetailsTime {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: var(--zy-distance-two) 0;

    .VideoMeetinDetailsTimeLeft,
    .VideoMeetinDetailsTimeRight {
      text-align: center;
      padding: 0 var(--zy-distance-one);
    }

    .VideoMeetinDetailsTimeCenter {
      width: 99px;
      height: 99px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      border-radius: 50%;
      border: 1px solid var(--zy-el-color-primary);
      background: var(--zy-el-color-primary-light-9);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 99px;
        height: 1px;
        background: linear-gradient(90deg, var(--zy-el-color-primary), #fff);
        top: 50%;
        right: 0;
        transform: translate(100%, -50%);
      }

      &::before {
        content: '';
        position: absolute;
        width: 99px;
        height: 1px;
        background: linear-gradient(90deg, #fff, var(--zy-el-color-primary));
        top: 50%;
        left: 0;
        transform: translate(-100%, -50%);
      }

      .VideoMeetinDetailsDuration {
        font-weight: bold;
        color: var(--zy-el-color-primary);
        font-size: calc(var(--zy-name-font-size) + 2px);
      }

      .VideoMeetinDetailsStatus {
        color: var(--zy-el-color-primary);
        font-size: var(--zy-name-font-size);
      }
    }

    .VideoMeetinDetailsTimeHours {
      font-weight: bold;
      font-size: calc(var(--zy-name-font-size) + 6px);
    }

    .VideoMeetinDetailsTimeDate {
      font-size: var(--zy-name-font-size);
    }
  }

  .LiveList {
    width: 100%;
    // display: flex;
    // flex-wrap: wrap;
    padding: 0 var(--zy-distance-one);

    .LiveName {
      font-weight: bold;
      font-size: 20px;
      line-height: var(--zy-line-height);
      margin-top: 10px;
    }

    .LiveContent {
      font-size: 16px;
      line-height: var(--zy-line-height);
      text-indent: 2em;
      margin-top: 5px
    }

    .player-container {
      width: 100%;
      height: 100%;
      margin-top: 5px;

      #video-player {
        width: 100%;
        height: 100%;
      }
    }

    .LiveInteractionBox {
      display: flex;
      align-items: center;
      margin-top: 10px;
      justify-content: space-between;
      width: 100%;
    }

    .LiveInteraction {
      display: flex;
      align-items: center;

      .LiveInteractionTotal {
        margin-right: 10px;
      }
    }
  }
}
</style>
